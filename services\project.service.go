package services

import (
	"net/http"

	"github.com/asaskevich/govalidator"
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IProjectService interface {
	Create(input *ProjectCreatePayload) (*models.Project, core.IError)
	Update(id string, input *ProjectUpdatePayload) (*models.Project, core.IError)
	Find(id string) (*models.Project, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
	PaginationWithFilters(filters *ProjectFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
	Delete(id string) core.IError
	GetUsages(id string, currentOnly bool, pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectUsage], core.IError)
	GetUsagesByCycle(id string, cycleID string, pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectUsage], core.IError)
}

type ProjectCreatePayload struct {
	Name            string   `json:"name"`
	Code            string   `json:"code"`
	AccountName     string   `json:"account_name"`
	PsaEmail        string   `json:"psa_email"`
	ProviderType    string   `json:"provider_type"`
	ContactName     *string  `json:"contact_name"`
	ContactPhone    *string  `json:"contact_phone"`
	ContactEmail    *string  `json:"contact_email"`
	RootAccountName string   `json:"root_account_name"`
	AccountHolder   string   `json:"account_holder"`
	Budget          *float64 `json:"budget"`
}

type ProjectUpdatePayload struct {
	Name            *string  `json:"name"`
	Code            *string  `json:"code"`
	ContactName     *string  `json:"contact_name"`
	ContactPhone    *string  `json:"contact_phone"`
	ContactEmail    *string  `json:"contact_email"`
	RootAccountName *string  `json:"root_account_name"`
	AccountHolder   *string  `json:"account_holder"`
	Budget          *float64 `json:"budget"`
	Status          *string  `json:"status"`
	AccountName     *string  `json:"account_name"`
	PsaEmail        *string  `json:"psa_email"`
}

type ProjectFilters struct {
	CreatedByID  string
	Status       models.ProjectStatus
	ProviderType models.ProjectProviderType
	AccountHolder string
}

type projectService struct {
	ctx core.IContext
}

func (s projectService) Create(input *ProjectCreatePayload) (*models.Project, core.IError) {
	user := s.ctx.GetUser()

	project := &models.Project{
		BaseModel:       models.NewBaseModel(),
		Name:            input.Name,
		ContactName:     input.ContactName,
		ContactPhone:    input.ContactPhone,
		ContactEmail:    input.ContactEmail,
		Budget:          input.Budget,
		Code:            input.Code,
		AccountName:     input.AccountName,
		PsaEmail:        input.PsaEmail,
		RootAccountName: input.RootAccountName,
		AccountHolder:   input.AccountHolder,
		ProviderType:    models.ProjectProviderType(input.ProviderType),
		Status:          models.ProjectStatusDraft,
		CreatedByID:     utils.ToPointer(user.ID),
	}

	ierr := repo.Project(s.ctx).Create(project)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(project.ID)
}

func (s projectService) Update(id string, input *ProjectUpdatePayload) (*models.Project, core.IError) {
	project, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	user := s.ctx.GetUser()

	// Update fields if provided
	if input.Code != nil {
		project.Code = *input.Code
	}
	if input.Name != nil {
		project.Name = *input.Name
	}
	if input.ContactName != nil {
		project.ContactName = input.ContactName
	}
	if input.ContactPhone != nil {
		project.ContactPhone = input.ContactPhone
	}
	if input.ContactEmail != nil {
		project.ContactEmail = input.ContactEmail
	}
	if input.Budget != nil {
		project.Budget = input.Budget
	}

	if input.AccountName != nil {
		project.AccountName = utils.ToNonPointer(input.AccountName)
	}

	if input.PsaEmail != nil {
		project.PsaEmail = utils.ToNonPointer(input.PsaEmail)
	}

	if input.RootAccountName != nil {
		project.RootAccountName = utils.ToNonPointer(input.RootAccountName)
	}

	if input.AccountHolder != nil {
		project.AccountHolder = utils.ToNonPointer(input.AccountHolder)
	}

	if input.Status != nil {
		project.Status = models.ProjectStatus(utils.ToNonPointer(input.Status))
	}

	if project.ProviderType == models.ProjectProviderTypeHWC && input.AccountName != nil {
		projectusageSvc := NewProjectUsageService(s.ctx)
		accountID, ierr := projectusageSvc.GetAccountID(utils.ToNonPointer(input.AccountName), project.RootAccountName)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}

		project.AccountID = accountID
	}

	// Update metadata
	project.UpdatedAt = utils.GetCurrentDateTime()
	project.UpdatedByID = utils.ToPointer(user.ID)

	ierr = repo.Project(s.ctx).Where("id = ?", id).Updates(project)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(project.ID)
}

func (s projectService) Find(id string) (*models.Project, core.IError) {
	return repo.Project(s.ctx, repo.ProjectWithAllRelations()).FindOne("id = ?", id)

}

func (s projectService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	return repo.Project(s.ctx, repo.ProjectBySearch(pageOptions.Q), repo.ProjectOrderBy(pageOptions), repo.ProjectWithAllRelations()).Pagination(pageOptions)
}

func (s projectService) PaginationWithFilters(filters *ProjectFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	var options []repo.ProjectOption

	// Add ordering and relations
	options = append(options, repo.ProjectOrderBy(pageOptions))
	options = append(options, repo.ProjectWithAllRelations())

	if filters.CreatedByID != "" {
		options = append(options, repo.ProjectByCreatedBy(filters.CreatedByID))
	}

	if filters.Status != "" {
		if govalidator.IsIn(
			string(filters.Status),
			string(models.ProjectStatusDraft),
			string(models.ProjectStatusActive),
			string(models.ProjectStatusClosed)) == false {
			ierr := core.Error{
				Status:  http.StatusBadRequest,
				Message: "status must be one of: DRAFT, ACTIVE, CLOSED",
				Code:    "INVALID_STATUS",
			}
			return nil, s.ctx.NewError(ierr, ierr)
		}
		options = append(options, repo.ProjectByStatus(filters.Status))
	}
	
	if filters.ProviderType != "" {
		if govalidator.IsIn(
			string(filters.ProviderType),
			string(models.ProjectProviderTypeHWC),
			string(models.ProjectProviderTypeAWS),
			string(models.ProjectProviderTypeCHM)) == false {
			ierr := core.Error{
				Status:  http.StatusBadRequest,
				Message: "provider_type must be one of: HWC, AWS, CHM",
				Code:    "INVALID_PROVIDER_TYPE",
			}
			return nil, s.ctx.NewError(ierr, ierr)
		}
		options = append(options, repo.ProjectByProviderType(filters.ProviderType))
	}
	
	if filters.AccountHolder != "" {
		options = append(options, repo.ProjectByAccountHolder(filters.AccountHolder))
	}

	if pageOptions.Q != "" {
		options = append(options, repo.ProjectBySearch(pageOptions.Q))
	}

	return repo.Project(s.ctx, options...).Pagination(pageOptions)
}

func (s projectService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	user := s.ctx.GetUser()

	// Soft delete with deleted_by tracking
	updates := map[string]interface{}{
		"deleted_at":    utils.GetCurrentDateTime(),
		"deleted_by_id": user.ID,
	}

	return repo.Project(s.ctx).Where("id = ?", id).Updates(updates)
}

func (s projectService) GetUsages(id string, currentOnly bool, pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectUsage], core.IError) {
	// First verify that the project exists
	_, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	var options []repo.ProjectUsageOption

	// Add basic options
	options = append(options, repo.ProjectUsageByProject(id))
	options = append(options, repo.ProjectUsageOrderBy(pageOptions))
	options = append(options, repo.ProjectUsageWithAllRelations())

	// Add cycle filter if currentOnly is true
	if currentOnly {
		options = append(options, repo.ProjectUsageByCurrentCycle())
	}

	// Add search if provided
	if pageOptions.Q != "" {
		options = append(options, repo.ProjectUsageBySearch(pageOptions.Q))
	}

	return repo.ProjectUsage(s.ctx, options...).Pagination(pageOptions)
}

func (s projectService) GetUsagesByCycle(id string, cycleID string, pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectUsage], core.IError) {
	// First verify that the project exists
	_, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Verify that the cycle exists
	cycleService := NewCycleService(s.ctx)
	_, ierr = cycleService.Find(cycleID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	var options []repo.ProjectUsageOption

	// Add basic options
	options = append(options, repo.ProjectUsageByProject(id))
	options = append(options, repo.ProjectUsageByCycle(cycleID))
	options = append(options, repo.ProjectUsageOrderBy(pageOptions))
	options = append(options, repo.ProjectUsageWithAllRelations())

	// Add search if provided
	if pageOptions.Q != "" {
		options = append(options, repo.ProjectUsageBySearch(pageOptions.Q))
	}

	return repo.ProjectUsage(s.ctx, options...).Pagination(pageOptions)
}

func NewProjectService(ctx core.IContext) IProjectService {
	return &projectService{ctx: ctx}
}
