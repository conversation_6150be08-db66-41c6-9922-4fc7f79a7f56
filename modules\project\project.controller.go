package project

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectController struct {
}

func (m ProjectController) Pagination(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)

	// Get filtering parameters
	filters := &services.ProjectFilters{
		CreatedByID:  c.Query<PERSON>m("created_by_id"),
		ProviderType: models.ProjectProviderType(c.Query<PERSON>aram("provider_type")),
		Status:       models.ProjectStatus(c.QueryParam("status")),
		AccountHolder: c.Query<PERSON>aram("account_holder"),
	}

	res, ierr := projectSvc.PaginationWithFilters(filters, c.GetPageOptions())

	if ierr != nil {
		return c.J<PERSON>(ierr.GetStatus(), ierr.JSON())
	}

	return c.<PERSON>(http.StatusOK, res)
}

func (m ProjectController) Find(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	project, err := projectSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Create(c core.IHTTPContext) error {
	input := &requests.ProjectCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectCreatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, project)
}

func (m ProjectController) Update(c core.IHTTPContext) error {
	input := &requests.ProjectUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectUpdatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Delete(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	err := projectSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m ProjectController) GetUsages(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)

	// Check if we should filter by current cycle only (default: true)
	currentOnly := true
	if c.QueryParam("all") == "true" {
		currentOnly = false
	}

	res, err := projectSvc.GetUsages(c.Param("id"), currentOnly, c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectController) GetUsagesByCycle(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)

	res, err := projectSvc.GetUsagesByCycle(c.Param("id"), c.Param("cycle_id"), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
