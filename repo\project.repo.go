package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm"
)

type ProjectOption func(repository.IRepository[models.Project])

var Project = func(c core.IContext, options ...ProjectOption) repository.IRepository[models.Project] {
	r := repository.New[models.Project](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func ProjectOrderBy(pageOptions *core.PageOptions) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func ProjectWithCreatedBy() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("CreatedBy")
	}
}

func ProjectWithUpdatedBy() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("UpdatedBy")
	}
}

func ProjectWithAllRelations() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("CreatedBy")
		c.Preload("UpdatedBy")
		c.Preload("ProjectUsage.Cycle", func(db *gorm.DB) *gorm.DB {
			return db.Limit(1).Order("created_at DESC")
		})
	}
}

func ProjectWithProjectUsages() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("ProjectUsages")
	}
}

func ProjectByCreatedBy(userID string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("created_by_id = ?", userID)
	}
}

func ProjectByProviderType(providerType models.ProjectProviderType) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("provider_type = ?", providerType)
	}
}

func ProjectByAccountHolder(accountHolderName string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("account_holder = ?", accountHolderName)
	}
}

func ProjectByStatus(status models.ProjectStatus) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("status = ?", status)
	}
}

func ProjectBySearch(search string) ProjectOption {
	if search == "" {
		return func(c repository.IRepository[models.Project]) {}
	}
	return func(c repository.IRepository[models.Project]) {
		searchPattern := "%" + search + "%"
		c.Where("name ILIKE ? OR contact_name ILIKE ? OR contact_email ILIKE ? OR code ILIKE ?", searchPattern, searchPattern, searchPattern, searchPattern)
	}
}
